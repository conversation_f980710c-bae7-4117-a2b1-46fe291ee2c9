#!/usr/bin/env python3
"""
测试经验管理器功能的脚本
"""

import asyncio
import json
import os
from strategies.experience_manager import ExperienceManager


async def test_experience_manager():
    """测试经验管理器的基本功能"""
    
    # 创建测试用的经验管理器
    test_file = "test_experiences.json"
    
    # 清理之前的测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
    
    manager = ExperienceManager(
        experience_file=test_file,
        similarity_threshold=0.7,
        max_experiences=100
    )
    
    print("=== 测试经验管理器 ===")
    
    # 测试1: 保存经验
    print("\n1. 测试保存经验...")
    
    test_experiences = [
        {
            "query": "查询服务器***********的TCP连接状态",
            "thought_process": ["需要查询TCP连接信息", "使用tcp_flow表查询", "分析连接状态"],
            "actions_taken": [
                {
                    "action_name": "query_stats_table",
                    "action_input": {"table": "tcp_flow", "filters": {"server_ip": "***********"}},
                    "observation": "查询成功，返回TCP连接数据"
                }
            ],
            "final_answer": "服务器***********当前有15个活跃的TCP连接",
            "execution_time": 2.5,
            "token_usage": 150
        },
        {
            "query": "分析网络跳包问题",
            "thought_process": ["检查重传率", "分析序列号", "确定跳包原因"],
            "actions_taken": [
                {
                    "action_name": "online_decode",
                    "action_input": {"time_range": "2025-06-16 14:00:00-14:00:01"},
                    "observation": "发现序列号跳变"
                }
            ],
            "final_answer": "检测到序列号跳变导致的跳包问题",
            "execution_time": 3.2,
            "token_usage": 200
        },
        {
            "query": "查看服务器***********的网络流量",
            "thought_process": ["查询流量统计", "分析流量模式", "生成报告"],
            "actions_taken": [
                {
                    "action_name": "query_stats_table", 
                    "action_input": {"table": "traffic_stats", "filters": {"server_ip": "***********"}},
                    "observation": "获取到流量统计数据"
                }
            ],
            "final_answer": "服务器***********的入站流量为100MB/s，出站流量为80MB/s",
            "execution_time": 1.8,
            "token_usage": 120
        }
    ]
    
    # 保存测试经验
    for exp in test_experiences:
        success = await manager.save_successful_experience(**exp)
        print(f"保存经验: {exp['query'][:30]}... - {'成功' if success else '失败'}")
    
    # 测试2: 查询相似经验
    print("\n2. 测试查询相似经验...")
    
    test_queries = [
        "查询服务器***********的连接信息",  # 应该与第一个经验相似
        "网络丢包分析",  # 应该与第二个经验相似
        "检查服务器***********流量状况",  # 应该与第三个经验相似
        "完全不相关的查询内容"  # 应该没有相似经验
    ]
    
    for query in test_queries:
        print(f"\n查询: {query}")
        similar_experiences = await manager.find_similar_experiences(query, top_k=3)
        
        if similar_experiences:
            print(f"找到 {len(similar_experiences)} 个相似经验:")
            for i, (exp, similarity) in enumerate(similar_experiences, 1):
                print(f"  {i}. 相似度: {similarity:.3f} - {exp.query[:40]}...")
        else:
            print("  没有找到相似经验")
    
    # 测试3: 生成提示词格式
    print("\n3. 测试生成提示词格式...")
    
    query = "查询服务器TCP状态"
    similar_experiences = await manager.find_similar_experiences(query, top_k=2)
    
    if similar_experiences:
        formatted_prompt = manager.format_similar_experiences_for_prompt(similar_experiences)
        print("生成的提示词格式:")
        print(formatted_prompt)
    
    # 测试4: 获取统计信息
    print("\n4. 经验库统计信息:")
    summary = manager.get_experience_summary()
    print(json.dumps(summary, indent=2, ensure_ascii=False))
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
        print(f"\n清理测试文件: {test_file}")
    
    print("\n=== 测试完成 ===")


async def test_embedding_functionality():
    """测试embedding功能"""
    print("\n=== 测试Embedding功能 ===")
    
    try:
        from strategies.experience_manager import EMBEDDING_AVAILABLE
        print(f"Embedding功能可用: {EMBEDDING_AVAILABLE}")
        
        if EMBEDDING_AVAILABLE:
            manager = ExperienceManager()
            
            # 测试embedding生成
            test_query = "测试查询"
            embedding = await manager._get_query_embedding(test_query)
            
            if embedding:
                print(f"成功生成embedding，维度: {len(embedding)}")
                print(f"前5个值: {embedding[:5]}")
            else:
                print("Embedding生成失败")
        else:
            print("将使用文本相似度作为备选方案")
            
    except Exception as e:
        print(f"测试embedding时出错: {e}")


def main():
    """主函数"""
    print("开始测试经验管理器...")
    
    # 运行基本功能测试
    asyncio.run(test_experience_manager())
    
    # 运行embedding功能测试
    asyncio.run(test_embedding_functionality())


if __name__ == "__main__":
    main()
