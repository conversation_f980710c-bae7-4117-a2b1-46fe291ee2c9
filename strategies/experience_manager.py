import json
import os
import hashlib
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import numpy as np
from pydantic import BaseModel

# 尝试导入embedding功能，如果不可用则使用简单的文本相似度
try:
    from HyperGraphRAG.hypergraphrag.llm import openai_embedding
    from HyperGraphRAG.hypergraphrag.utils import cosine_similarity
    EMBEDDING_AVAILABLE = True
except ImportError:
    EMBEDDING_AVAILABLE = False
    print("Warning: HyperGraphRAG embedding not available, using simple text similarity")


@dataclass
class QueryExperience:
    """查询经验数据结构"""
    query: str
    query_hash: str
    thought_process: List[str]  # 思考过程
    actions_taken: List[Dict[str, Any]]  # 执行的动作
    final_answer: str
    success: bool
    timestamp: str
    execution_time: float
    token_usage: int
    embedding: Optional[List[float]] = None  # 查询的向量表示


class ExperienceManager:
    """经验管理器 - 负责保存和检索成功的查询经验"""

    def __init__(self, experience_file: str = "react_experiences.json",
                 embedding_model: str = "text-embedding-3-small",
                 similarity_threshold: float = 0.8,
                 max_experiences: int = 1000,
                 config_file: str = "experience_config.json"):
        """
        初始化经验管理器

        Args:
            experience_file: 经验存储文件路径
            embedding_model: 用于生成embedding的模型
            similarity_threshold: 相似度阈值，超过此值认为是相似查询
            max_experiences: 最大保存经验数量
            config_file: 配置文件路径
        """
        # 加载配置
        self.config = self._load_config(config_file)

        # 使用配置或默认值
        exp_config = self.config.get("experience_manager", {})
        self.experience_file = exp_config.get("experience_file", experience_file)
        self.embedding_model = exp_config.get("embedding_model", embedding_model)
        self.similarity_threshold = exp_config.get("similarity_threshold", similarity_threshold)
        self.max_experiences = exp_config.get("max_experiences", max_experiences)
        self.enabled = exp_config.get("enabled", True)
        self.auto_save = exp_config.get("auto_save", True)
        self.use_embedding = exp_config.get("use_embedding", True)
        self.top_k_similar = exp_config.get("top_k_similar", 3)

        self.experiences: Dict[str, QueryExperience] = {}
        if self.enabled:
            self._load_experiences()

    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"Warning: Could not load config file {config_file}: {e}")
        return {}
    
    def _load_experiences(self):
        """从文件加载已保存的经验"""
        if os.path.exists(self.experience_file):
            try:
                with open(self.experience_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for key, exp_data in data.items():
                        self.experiences[key] = QueryExperience(**exp_data)
                print(f"Loaded {len(self.experiences)} experiences from {self.experience_file}")
            except Exception as e:
                print(f"Error loading experiences: {e}")
                self.experiences = {}
    
    def _save_experiences(self):
        """保存经验到文件"""
        try:
            # 如果经验数量超过最大值，删除最旧的经验
            if len(self.experiences) > self.max_experiences:
                sorted_experiences = sorted(
                    self.experiences.items(), 
                    key=lambda x: x[1].timestamp
                )
                # 保留最新的经验
                self.experiences = dict(sorted_experiences[-self.max_experiences:])
            
            # 转换为可序列化的格式
            serializable_data = {}
            for key, exp in self.experiences.items():
                serializable_data[key] = asdict(exp)
            
            with open(self.experience_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_data, f, ensure_ascii=False, indent=2)
            print(f"Saved {len(self.experiences)} experiences to {self.experience_file}")
        except Exception as e:
            print(f"Error saving experiences: {e}")
    
    def _generate_query_hash(self, query: str) -> str:
        """生成查询的哈希值作为唯一标识"""
        return hashlib.md5(query.encode('utf-8')).hexdigest()
    
    async def _get_query_embedding(self, query: str) -> Optional[List[float]]:
        """获取查询的向量表示"""
        if not EMBEDDING_AVAILABLE:
            return None
        
        try:
            embedding = await openai_embedding([query])
            return embedding[0].tolist()
        except Exception as e:
            print(f"Error generating embedding: {e}")
            return None
    
    def _calculate_text_similarity(self, query1: str, query2: str) -> float:
        """计算文本相似度（改进的基于词汇重叠和字符相似度的方法）"""
        # 转换为小写并分词
        words1 = set(query1.lower().split())
        words2 = set(query2.lower().split())

        if not words1 or not words2:
            return 0.0

        # 计算词汇重叠相似度（Jaccard相似度）
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        jaccard_similarity = len(intersection) / len(union) if union else 0.0

        # 计算字符级别的相似度（简单的字符重叠）
        chars1 = set(query1.lower().replace(' ', ''))
        chars2 = set(query2.lower().replace(' ', ''))
        char_intersection = chars1.intersection(chars2)
        char_union = chars1.union(chars2)
        char_similarity = len(char_intersection) / len(char_union) if char_union else 0.0

        # 计算长度相似度
        len1, len2 = len(query1), len(query2)
        length_similarity = min(len1, len2) / max(len1, len2) if max(len1, len2) > 0 else 0.0

        # 综合相似度（加权平均）
        final_similarity = (
            0.5 * jaccard_similarity +  # 词汇重叠权重最高
            0.3 * char_similarity +     # 字符重叠次之
            0.2 * length_similarity     # 长度相似度权重最低
        )

        return final_similarity
    
    async def find_similar_experiences(self, query: str, top_k: int = 3) -> List[Tuple[QueryExperience, float]]:
        """
        查找与当前查询相似的历史经验
        
        Args:
            query: 当前查询
            top_k: 返回最相似的top_k个经验
            
        Returns:
            List of (experience, similarity_score) tuples
        """
        if not self.experiences:
            return []
        
        similarities = []
        
        if EMBEDDING_AVAILABLE:
            # 使用embedding计算相似度
            query_embedding = await self._get_query_embedding(query)
            if query_embedding is not None:
                for exp in self.experiences.values():
                    if exp.embedding is not None:
                        similarity = cosine_similarity(
                            np.array(query_embedding), 
                            np.array(exp.embedding)
                        )
                        similarities.append((exp, float(similarity)))
        
        # 如果embedding不可用或失败，使用文本相似度
        if not similarities:
            for exp in self.experiences.values():
                similarity = self._calculate_text_similarity(query, exp.query)
                similarities.append((exp, similarity))
        
        # 按相似度排序并返回top_k
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:top_k]
    
    async def save_successful_experience(self, 
                                       query: str,
                                       thought_process: List[str],
                                       actions_taken: List[Dict[str, Any]],
                                       final_answer: str,
                                       execution_time: float,
                                       token_usage: int) -> bool:
        """
        保存成功的查询经验
        
        Args:
            query: 原始查询
            thought_process: 思考过程列表
            actions_taken: 执行的动作列表
            final_answer: 最终答案
            execution_time: 执行时间
            token_usage: token使用量
            
        Returns:
            是否保存成功
        """
        try:
            query_hash = self._generate_query_hash(query)
            
            # 生成embedding
            embedding = await self._get_query_embedding(query)
            
            experience = QueryExperience(
                query=query,
                query_hash=query_hash,
                thought_process=thought_process,
                actions_taken=actions_taken,
                final_answer=final_answer,
                success=True,
                timestamp=datetime.now().isoformat(),
                execution_time=execution_time,
                token_usage=token_usage,
                embedding=embedding
            )
            
            self.experiences[query_hash] = experience
            self._save_experiences()
            
            print(f"Saved successful experience for query: {query[:50]}...")
            return True
            
        except Exception as e:
            print(f"Error saving experience: {e}")
            return False
    
    def get_experience_summary(self) -> Dict[str, Any]:
        """获取经验库的统计信息"""
        if not self.experiences:
            return {"total_experiences": 0}
        
        total = len(self.experiences)
        successful = sum(1 for exp in self.experiences.values() if exp.success)
        avg_execution_time = np.mean([exp.execution_time for exp in self.experiences.values()])
        avg_token_usage = np.mean([exp.token_usage for exp in self.experiences.values()])
        
        return {
            "total_experiences": total,
            "successful_experiences": successful,
            "success_rate": successful / total if total > 0 else 0,
            "avg_execution_time": avg_execution_time,
            "avg_token_usage": avg_token_usage,
            "embedding_available": EMBEDDING_AVAILABLE
        }
    
    def format_similar_experiences_for_prompt(self, similar_experiences: List[Tuple[QueryExperience, float]]) -> str:
        """
        将相似经验格式化为可用于prompt的文本
        
        Args:
            similar_experiences: 相似经验列表
            
        Returns:
            格式化的经验文本
        """
        if not similar_experiences:
            return ""
        
        formatted_text = "## 相似查询的成功经验参考：\n\n"
        
        for i, (exp, similarity) in enumerate(similar_experiences, 1):
            formatted_text += f"### 经验 {i} (相似度: {similarity:.3f})\n"
            formatted_text += f"**查询**: {exp.query}\n"
            formatted_text += f"**思考过程**: {' -> '.join(exp.thought_process[:3])}...\n"
            formatted_text += f"**关键动作**: {len(exp.actions_taken)} 个动作\n"
            formatted_text += f"**最终答案**: {exp.final_answer[:100]}...\n"
            formatted_text += f"**执行时间**: {exp.execution_time:.2f}秒\n\n"
        
        formatted_text += "请参考以上成功经验来处理当前查询。\n\n"
        return formatted_text
