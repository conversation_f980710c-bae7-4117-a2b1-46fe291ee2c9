#!/usr/bin/env python3
"""
测试经验管理器功能的独立脚本
"""

import json
import os
import asyncio
from unittest.mock import Mock, MagicMock
import sys

# 不导入ReAct策略，直接测试经验管理器
from strategies.experience_manager import <PERSON>Manager


def create_mock_session():
    """创建模拟的session对象"""
    session = Mock()
    
    # 模拟LLM响应
    mock_llm = Mock()
    mock_model = Mock()
    mock_model.llm = mock_llm
    session.model = mock_model
    
    # 模拟工具调用
    mock_tool = Mock()
    session.tool = mock_tool
    
    return session


def create_mock_agent_config():
    """创建模拟的agent配置"""
    from dify_plugin.interfaces.agent import AgentModelConfig
    
    config = AgentModelConfig(
        provider="openai",
        model="gpt-4",
        completion_params={"temperature": 0.7}
    )
    return config


def test_experience_manager_integration():
    """测试经验管理器功能"""
    print("=== 测试经验管理器功能 ===")

    # 清理测试文件
    test_files = ["test_react_experiences.json", "test_experience_config.json"]
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)

    # 创建测试配置
    test_config = {
        "experience_manager": {
            "enabled": True,
            "experience_file": "test_react_experiences.json",
            "similarity_threshold": 0.3,  # 降低阈值便于测试
            "max_experiences": 100,
            "auto_save": True,
            "top_k_similar": 2
        }
    }

    with open("test_experience_config.json", "w", encoding="utf-8") as f:
        json.dump(test_config, f, indent=2)

    # 1. 测试经验管理器初始化
    print("\n1. 测试经验管理器初始化...")

    try:
        # 直接测试经验管理器
        manager = ExperienceManager(config_file="test_experience_config.json")

        print(f"经验管理器启用状态: {manager.enabled}")
        print(f"相似度阈值: {manager.similarity_threshold}")
        print(f"最大经验数: {manager.max_experiences}")

    except Exception as e:
        print(f"初始化失败: {e}")
        return False
    
    # 2. 测试手动添加经验
    print("\n2. 测试手动添加经验...")
    
    async def add_test_experiences():
        # 添加一些测试经验
        test_experiences = [
            {
                "query": "查询TCP连接状态",
                "thought_process": ["需要查询TCP信息", "使用tcp_flow表"],
                "actions_taken": [{"action_name": "query_stats", "action_input": {}, "observation": "成功"}],
                "final_answer": "找到10个TCP连接",
                "execution_time": 2.0,
                "token_usage": 100
            },
            {
                "query": "分析网络延迟",
                "thought_process": ["检查延迟指标", "分析RTT数据"],
                "actions_taken": [{"action_name": "analyze_latency", "action_input": {}, "observation": "延迟正常"}],
                "final_answer": "平均延迟50ms",
                "execution_time": 1.5,
                "token_usage": 80
            }
        ]
        
        for exp in test_experiences:
            success = await manager.save_successful_experience(**exp)
            print(f"保存经验: {exp['query']} - {'成功' if success else '失败'}")

    # 运行异步函数
    asyncio.run(add_test_experiences())

    # 3. 测试经验检索
    print("\n3. 测试经验检索...")

    async def test_experience_retrieval():
        test_queries = [
            "查询TCP连接信息",  # 应该匹配第一个经验
            "网络延迟分析",     # 应该匹配第二个经验
            "完全不相关的查询"   # 应该没有高相似度匹配
        ]

        for query in test_queries:
            print(f"\n查询: {query}")
            similar_experiences = await manager.find_similar_experiences(
                query, top_k=2
            )

            if similar_experiences:
                for i, (exp, similarity) in enumerate(similar_experiences, 1):
                    print(f"  {i}. 相似度: {similarity:.3f} - {exp.query}")

                # 测试高相似度过滤
                high_sim = [
                    (exp, sim) for exp, sim in similar_experiences
                    if sim >= manager.similarity_threshold
                ]

                if high_sim:
                    print(f"  高相似度经验: {len(high_sim)} 个")
                    context = manager.format_similar_experiences_for_prompt(high_sim)
                    print(f"  生成上下文长度: {len(context)} 字符")
                else:
                    print("  没有高相似度经验")
            else:
                print("  没有找到相似经验")

    asyncio.run(test_experience_retrieval())

    # 4. 测试统计信息
    print("\n4. 测试统计信息...")
    summary = manager.get_experience_summary()
    print("经验库统计:")
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    # 5. 测试配置禁用
    print("\n5. 测试配置禁用...")
    
    # 创建禁用配置
    disabled_config = {
        "experience_manager": {
            "enabled": False
        }
    }
    
    with open("test_experience_config.json", "w", encoding="utf-8") as f:
        json.dump(disabled_config, f, indent=2)
    
    # 重新初始化
    disabled_manager = ExperienceManager(config_file="test_experience_config.json")
    print(f"禁用状态: {not disabled_manager.enabled}")
    
    # 清理测试文件
    for file in test_files + ["test_experience_config.json"]:
        if os.path.exists(file):
            os.remove(file)
    
    print("\n=== 集成测试完成 ===")
    return True


def test_react_strategy_mock():
    """测试ReAct策略的模拟执行"""
    print("\n=== 测试ReAct策略模拟执行 ===")
    
    try:
        # 创建策略实例
        strategy = ReActAgentStrategy()
        
        # 设置模拟session
        strategy.session = create_mock_session()
        
        # 创建测试参数
        test_params = {
            "query": "测试查询TCP连接状态",
            "instruction": "请帮助分析网络连接",
            "model": create_mock_agent_config(),
            "tools": [],
            "maximum_iterations": 1
        }
        
        print("ReAct策略初始化成功")
        print(f"经验管理器状态: {'启用' if strategy.experience_manager.enabled else '禁用'}")
        
        # 注意：这里不实际调用_invoke方法，因为需要复杂的mock设置
        # 只测试初始化和配置
        
        return True
        
    except Exception as e:
        print(f"ReAct策略测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始ReAct策略与经验管理器集成测试...")
    
    # 测试1: 经验管理器集成
    success1 = test_experience_manager_integration()
    
    # 测试2: ReAct策略模拟
    success2 = test_react_strategy_mock()
    
    if success1 and success2:
        print("\n✅ 所有测试通过！")
        print("\n功能特性:")
        print("- ✅ 经验自动保存和加载")
        print("- ✅ 智能相似度匹配")
        print("- ✅ 配置文件支持")
        print("- ✅ 启用/禁用控制")
        print("- ✅ 统计信息查看")
        print("- ✅ 提示词上下文生成")
        
        print("\n使用建议:")
        print("1. 调整similarity_threshold参数优化匹配效果")
        print("2. 根据需要设置max_experiences限制经验数量")
        print("3. 监控经验文件大小，定期清理")
        print("4. 在生产环境中启用embedding以获得更好效果")
        
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
    
    return success1 and success2


if __name__ == "__main__":
    main()
