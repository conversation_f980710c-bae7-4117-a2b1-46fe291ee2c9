# ReAct策略经验管理功能

## 功能概述

为ReAct策略增加了经验保存和复用功能，可以自动保存成功的查询经验，并在处理相似查询时提供参考，实现few-shot学习效果。

## 主要特性

### 1. 自动经验保存
- 自动保存成功执行的查询经验
- 包含完整的思考过程、执行动作和最终结果
- 记录执行时间和token使用量等元数据

### 2. 智能经验检索
- 支持基于embedding的语义相似度匹配（如果可用）
- 备选文本相似度算法（词汇重叠+字符相似度+长度相似度）
- 可配置相似度阈值，返回top-k相似经验

### 3. 经验复用
- 将相似经验格式化为提示词上下文
- 为LLM提供成功案例参考
- 提高查询处理的准确性和效率

## 配置参数

```python
experience_manager = ExperienceManager(
    experience_file="react_experiences.json",  # 经验存储文件
    embedding_model="text-embedding-3-small",  # embedding模型
    similarity_threshold=0.8,                  # 相似度阈值
    max_experiences=1000                       # 最大经验数量
)
```

## 经验数据结构

```python
@dataclass
class QueryExperience:
    query: str                          # 原始查询
    query_hash: str                     # 查询哈希值
    thought_process: List[str]          # 思考过程
    actions_taken: List[Dict[str, Any]] # 执行的动作
    final_answer: str                   # 最终答案
    success: bool                       # 是否成功
    timestamp: str                      # 时间戳
    execution_time: float               # 执行时间
    token_usage: int                    # token使用量
    embedding: Optional[List[float]]    # 查询向量表示
```

## 使用示例

### 1. 基本使用
ReAct策略会自动使用经验管理功能，无需额外配置。

### 2. 手动测试
```bash
python test_experience_manager.py
```

### 3. 查看经验统计
```python
from strategies.experience_manager import ExperienceManager

manager = ExperienceManager()
summary = manager.get_experience_summary()
print(summary)
```

## 工作流程

### 1. 查询开始时
1. 接收用户查询
2. 计算查询的向量表示（如果embedding可用）
3. 检索相似的历史成功经验
4. 将相似经验格式化为提示词上下文
5. 将经验上下文添加到系统指令中

### 2. 查询结束时
1. 检查是否成功完成查询
2. 收集思考过程和执行动作
3. 计算执行时间和token使用量
4. 保存成功经验到文件

## 相似度计算

### 1. Embedding相似度（推荐）
- 使用OpenAI embedding API
- 计算余弦相似度
- 语义理解更准确

### 2. 文本相似度（备选）
- 词汇重叠相似度（Jaccard）
- 字符级别相似度
- 长度相似度
- 加权综合计算

## 经验文件格式

```json
{
  "query_hash_1": {
    "query": "查询服务器TCP连接状态",
    "query_hash": "abc123...",
    "thought_process": ["需要查询TCP信息", "使用tcp_flow表"],
    "actions_taken": [
      {
        "action_name": "query_stats_table",
        "action_input": {"table": "tcp_flow"},
        "observation": "查询成功"
      }
    ],
    "final_answer": "服务器有15个活跃连接",
    "success": true,
    "timestamp": "2025-01-17T10:30:00",
    "execution_time": 2.5,
    "token_usage": 150,
    "embedding": [0.1, 0.2, ...]
  }
}
```

## 性能优化

### 1. 经验数量限制
- 默认最多保存1000个经验
- 自动删除最旧的经验
- 避免文件过大影响性能

### 2. 相似度阈值
- 默认阈值0.8，只使用高相似度经验
- 可根据实际效果调整
- 避免低质量经验干扰

### 3. 批量处理
- 支持异步embedding生成
- 批量相似度计算
- 优化响应时间

## 故障处理

### 1. Embedding不可用
- 自动降级到文本相似度
- 不影响基本功能
- 输出警告信息

### 2. 文件读写错误
- 异常捕获和日志记录
- 不影响主要查询流程
- 优雅降级处理

### 3. 内存使用
- 延迟加载经验数据
- 定期清理过期经验
- 监控内存使用情况

## 扩展功能

### 1. 经验质量评估
- 根据用户反馈调整经验权重
- 自动过滤低质量经验
- 持续优化经验库

### 2. 多模态经验
- 支持图片、表格等多种数据类型
- 扩展经验表示能力
- 提高复用效果

### 3. 分布式经验共享
- 支持多实例经验同步
- 团队经验共享
- 集中式经验管理

## 注意事项

1. **隐私保护**: 经验文件可能包含敏感信息，注意访问控制
2. **存储空间**: 定期清理过期经验，避免磁盘空间不足
3. **版本兼容**: 升级时注意经验文件格式兼容性
4. **性能监控**: 监控经验检索对查询响应时间的影响

## 更新日志

- **v1.0.0**: 初始版本，支持基本经验保存和检索
- 支持embedding和文本相似度两种匹配方式
- 自动经验管理和文件存储
- 完整的测试用例和文档
